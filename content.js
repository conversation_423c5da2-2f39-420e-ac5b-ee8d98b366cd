let liElements
let liLength
let index
// let currentScrollPosition = 0
let work = true

async function main() {
  updateLi()
  while (index + 1 !== liLength && work) {
    await pause(100)
    updateLi()
    let currentLi = liElements[index]

    // Проверяем, нужно ли пропустить эту конференцию
    if (itsGM(currentLi) && await shouldSkipConference(currentLi)) {
      console.log('Пропускаем конференцию с защищенным ником')
      clickOnElement(index + 1)
      await pause(100)
      continue
    }

    if (itsGM(currentLi)) await getAndDeleteAllMembers()
    await pause(150)
    clickOnElement(index + 1)
    await pause(100)
    if (itsGM(currentLi)) await leave(currentLi)
  }
}

async function leave(el, i) {
  if (el) el.querySelector('[class^="closeButton_"]').click()
  if (i) liElements[i].querySelector('[class^="closeButton_"]').click()
  await pause(150)
  document.querySelector('button[type="submit"]').click()
}

function itsGM(el, i) {
  if (el) return el.querySelector('a').getAttribute('aria-label').includes('group message')
  if (i) return liElements[i].querySelector('a').getAttribute('aria-label').includes('group message')
}

async function shouldSkipConference(el, i) {
  let element = el || liElements[i]
  if (!element) return false

  // Получаем всех участников конференции
  let members = document.querySelectorAll('[class^="member_"]')

  // Защищенные ники
  let protectedNames = ['melktrupp', 'melk trupp']

  // Проверяем каждого участника
  for (let member of members) {
    try {
      // Получаем имя пользователя из различных возможных элементов
      let usernameElement = member.querySelector('[class*="username"]') ||
        member.querySelector('[class*="name"]') ||
        member.querySelector('[class*="displayName"]') ||
        member.querySelector('[class*="nick"]') ||
        member.querySelector('span') ||
        member.querySelector('div')

      if (usernameElement) {
        let username = usernameElement.textContent.toLowerCase().trim()

        // Проверяем, содержит ли имя пользователя один из защищенных ников
        if (protectedNames.some(name => username.includes(name))) {
          console.log(`Найден защищенный пользователь: ${usernameElement.textContent}`)
          return true
        }
      }

      // Дополнительная проверка: ищем по aria-label или title
      let ariaLabel = member.getAttribute('aria-label') || member.getAttribute('title') || ''
      if (ariaLabel) {
        let labelText = ariaLabel.toLowerCase().trim()
        if (protectedNames.some(name => labelText.includes(name))) {
          console.log(`Найден защищенный пользователь в aria-label: ${ariaLabel}`)
          return true
        }
      }

    } catch (error) {
      // Игнорируем ошибки при получении имени пользователя
      continue
    }
  }

  return false
}

function clickOnElement(i) {
  if (i + 2 === liLength) {
    alert('Дошли до конца, если удалило не все - перезапустите')
    work = false
  }
  liElements[i].querySelector('[class^="name__"]').click()
}

function updateLi() {
  liElements = document.querySelectorAll('li[role="listitem"]')
  index = Array.from(liElements)
    .flatMap((y) => Array.from(y.children))
    .findIndex((element) => element.classList.value.match(/selected\w+/))
  liLength = Array.from(liElements).length
}

async function getAndDeleteAllMembers() {
  let members = document.querySelectorAll('[class^="member_"]')
  for (let el of members) {
    await deleteMember(el)
  }
}

async function deleteMember(el) {
  let event = new MouseEvent('contextmenu', {
    bubbles: true,
    cancelable: true,
    view: window,
  })

  el.dispatchEvent(event)
  await pause(150)
  let menu = document.querySelector('#user-context-remove')
  if (menu) menu.click()
}

async function pause(milliseconds) {
  return new Promise((resolve) => setTimeout(resolve, milliseconds))
}

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === 'start') {
    work = true
    main()
  }
})

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action === 'stop') {
    work = false
  }
})

chrome.runtime.onMessage.addListener(async function (request, sender, sendResponse) {
  if (request.action === 'deletewan') {
    updateLi()
    let currentLi = liElements[index]

    // Проверяем, нужно ли пропустить эту конференцию
    if (itsGM(currentLi) && await shouldSkipConference(currentLi)) {
      console.log('Пропускаем конференцию с защищенным ником')
      clickOnElement(index + 1)
      await pause(100)
      return
    }

    if (itsGM(currentLi)) await getAndDeleteAllMembers()
    await pause(150)
    clickOnElement(index + 1)
    await pause(100)
    if (itsGM(currentLi)) await leave(currentLi)
  }
})

// function getName() {
//   let name = document.querySelector('input[name="channel_name"]')
//   if (name) return name.value
// }

// function setFirst() {
//   let scrollElem = document.querySelector('li[role="listitem"]').parentNode.parentNode
//   currentScrollPosition = scrollElem.scrollTop || 0
//   currentScrollPosition += getActiveElem() * 35
//   scrollElem.scrollTop = currentScrollPosition
// }
